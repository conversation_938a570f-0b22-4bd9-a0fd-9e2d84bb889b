# Auspex Records

A complete web platform for Auspex Records, featuring a modern React frontend and AWS serverless backend with separate staging and production environments.

## 🌐 Live Sites

| Environment    | URL                                                        | Status  |
| -------------- | ---------------------------------------------------------- | ------- |
| **Production** | [auspexrecords.com](https://auspexrecords.com)             | ✅ Live |
| **Staging**    | [stage.auspexrecords.com](https://stage.auspexrecords.com) | ✅ Live |

## 📁 Project Structure

```
auspex/
├── auspex-website/         # Main website application
│   ├── src/               # React frontend source code
│   ├── terraform/         # Infrastructure as Code
│   ├── lambda/            # Serverless API functions
│   └── dist/              # Built frontend assets
└── tools/                 # Audio processing and automation tools
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- AWS CLI configured
- Terraform 1.2+

### Development Setup

```bash
# Navigate to website directory
cd auspex-website

# Install dependencies
npm install

# Start development server
npm run dev
```

### Deployment

```bash
# Deploy to staging
npm run deploy:staging

# Deploy to production
npm run deploy:prod
```

## 🏗️ Architecture

### Frontend

- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **Vite** for build tooling

### Backend (AWS Serverless)

- **S3** for static website hosting
- **CloudFront** for global CDN
- **API Gateway** + **Lambda** for serverless API
- **DynamoDB** for data storage
- **Terraform** for Infrastructure as Code

## 📂 Project Components

### [auspex-website/](./auspex-website/)

The main website application with React frontend and AWS infrastructure.

- See [Website Documentation](./auspex-website/README.md) for detailed setup and deployment

### [tools/](./tools/)

Audio processing and automation tools for managing music releases.

- See [Tools Documentation](./tools/README.md) for usage instructions

## 🎵 Features

- **Music Releases** - Browse and stream releases with embedded YouTube videos
- **Live Performances** - Watch recorded live performances
- **Multi-Platform Links** - Direct links to Spotify, Bandcamp, SoundCloud, etc.
- **Download Options** - Multiple audio formats (FLAC, MP3, WAV, etc.)
- **Interactive Animations** - Dynamic background with floating album covers
- **Responsive Design** - Optimized for all devices with dark theme

## 🔄 Deployment Workflow

1. Make changes to the codebase
2. Test locally with `npm run dev` (in auspex-website/)
3. Deploy to staging with `npm run deploy:staging`
4. Test at https://stage.auspexrecords.com
5. Deploy to production with `npm run deploy:prod`
6. Verify at https://auspexrecords.com

## 🤝 Contributing

1. Make changes in the appropriate directory (`auspex-website/` or `tools/`)
2. Test locally before deploying
3. Always test on staging before production
4. Follow the code standards outlined in each component's README

## 📄 License

Private project for Auspex Records.
