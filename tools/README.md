# Auspex Records Tools

Automation tools for processing and managing music releases for Auspex Records.

## [`convert_s3_codecs.py`](convert_s3_codecs.py)

Automated audio processing pipeline that converts WAV files into multiple formats (MP3, FLAC, AAC, etc.) and creates download packages for the website.

### How It Works

1. Compares S3 buckets to identify new releases
2. Downloads raw audio files from preprocessing bucket
3. Converts audio to multiple formats using FFmpeg
4. Packages releases into format-specific ZIP files
5. Uploads processed releases to public releases bucket

### Requirements

- Python 3.12+
- FFmpeg with codec support
- AWS CLI configured
- S3 buckets: `auspex-records-preprocess` and `auspex-records-releases`

### Input Format

Place WAV files in preprocessing bucket:

```
auspex-records-preprocess/
└── Artist Name - Album Title/
    ├── 01 - Track Name.wav
    ├── 02 - Another Track.wav
    └── cover.jpg (optional)
```

### Output Formats

Creates ZIP files for each format: MP3 (320k, V0), FLAC, AAC, Ogg Vorbis, ALAC, WAV, AIFF

### Setup

1. Install dependencies:

   ```bash
   pip install -r requirements.txt
   brew install ffmpeg  # or equivalent for your OS
   ```

2. Configure AWS CLI:

   ```bash
   aws configure
   ```

3. Ensure S3 buckets exist and you have appropriate permissions

### Usage

```bash
cd tools
python convert_s3_codecs.py
```

The script processes new releases by downloading, converting, packaging, and uploading to the releases bucket.

---

## [`create_youtube_video.py`](create_youtube_video.py)

### Overview

A powerful video creation tool that generates high-quality 4K YouTube videos from audio tracks and static images. Perfect for music labels to create professional-looking videos for streaming platforms, social media, and promotional content.

### How It Works

The script combines audio files with static images to create **4K resolution videos** (3840x2160) optimized for YouTube and other video platforms. It uses FFmpeg to:

1. **Loop a static image** for the duration of the audio
2. **Encode video** in high-quality H.264 format
3. **Process audio** with AAC codec at 320kbps
4. **Output 4K videos** ready for upload to YouTube

### Input Requirements

#### Audio Files

**Supported Formats:**

- `.mp3` - Most common format
- `.wav` - High quality uncompressed
- `.flac` - Lossless compression
- `.m4a` - Apple/AAC format
- `.ogg` - Ogg Vorbis format
- `.aac` - Raw AAC format

**Quality Recommendations:**

- **Minimum**: 320kbps MP3 or equivalent
- **Recommended**: FLAC or high-bitrate formats for best results
- **Duration**: Any length (script automatically matches video duration to audio)

#### Image Files

**Supported Formats:**

- `.jpg` / `.jpeg` - Most common, good compression
- `.png` - Supports transparency, larger files
- `.bmp` - Uncompressed bitmap

**Quality Requirements:**

- **Minimum Resolution**: 1920x1080 (HD)
- **Recommended**: 3840x2160 (4K) or higher
- **Aspect Ratio**: 16:9 for best YouTube compatibility
- **File Size**: No strict limit, but larger images take longer to process

### Usage Modes

#### Mode 1: Single Image for All Videos

Use the same image (like album artwork) for all tracks:

```bash
python create_youtube_video.py \
    --audio-folder "path/to/audio/files" \
    --image "path/to/album-cover.jpg" \
    --output-folder "youtube_videos"
```

#### Mode 2: Matching Images for Each Track

Use different images for each track (images must match audio filenames):

```bash
python create_youtube_video.py \
    --audio-folder "path/to/audio/files" \
    --image "path/to/default-image.jpg" \
    --image-folder "path/to/images" \
    --output-folder "youtube_videos"
```

### Command Line Arguments

| Argument          | Required | Description                                  | Example         |
| ----------------- | -------- | -------------------------------------------- | --------------- |
| `--audio-folder`  | ✅       | Path to folder containing audio files        | `./audio/album` |
| `--image`         | ✅       | Default image file for all videos            | `./cover.jpg`   |
| `--output-folder` | ❌       | Output directory (default: `youtube_videos`) | `./videos`      |
| `--image-folder`  | ❌       | Folder with images matching audio filenames  | `./images`      |

### Input/Output Examples

#### Example 1: Album Release Videos

**Input Structure:**

```
my-album/
├── audio/
│   ├── 01 - Track One.mp3
│   ├── 02 - Track Two.mp3
│   └── 03 - Track Three.mp3
├── cover.jpg
└── images/
    ├── 01 - Track One.jpg    # Optional: track-specific image
    └── 02 - Track Two.png    # Optional: track-specific image
```

**Command:**

```bash
python create_youtube_video.py \
    --audio-folder "my-album/audio" \
    --image "my-album/cover.jpg" \
    --image-folder "my-album/images" \
    --output-folder "my-album/videos"
```

**Output Structure:**

```
my-album/videos/
├── 01 - Track One.mp4      # Uses 01 - Track One.jpg
├── 02 - Track Two.mp4      # Uses 02 - Track Two.png
└── 03 - Track Three.mp4    # Uses cover.jpg (no matching image)
```

#### Example 2: Simple Album Videos

**Input Structure:**

```
simple-album/
├── audio/
│   ├── Song A.wav
│   ├── Song B.flac
│   └── Song C.mp3
└── album-artwork.png
```

**Command:**

```bash
python create_youtube_video.py \
    --audio-folder "simple-album/audio" \
    --image "simple-album/album-artwork.png"
```

**Output Structure:**

```
youtube_videos/              # Default output folder
├── Song A.mp4              # All use album-artwork.png
├── Song B.mp4
└── Song C.mp4
```

### Technical Specifications

#### Video Output Settings

- **Resolution**: 4K (3840x2160 pixels)
- **Frame Rate**: 24 fps (standard for YouTube)
- **Video Codec**: H.264 (libx264)
- **Quality**: CRF 18 (high quality, range 0-51)
- **Preset**: veryslow (maximum quality encoding)
- **Pixel Format**: yuv420p (universal compatibility)

#### Audio Output Settings

- **Codec**: AAC (best compatibility)
- **Bitrate**: 320kbps (high quality)
- **Sample Rate**: Preserved from source
- **Channels**: Preserved from source

#### File Sizes (Approximate)

- **3-minute track**: ~150-300MB per video
- **5-minute track**: ~250-500MB per video
- **10-minute track**: ~500MB-1GB per video

_File sizes depend on image complexity and audio content_

### Prerequisites & Setup

#### System Requirements

- **Python 3.6+**
- **FFmpeg** with H.264 and AAC support
- **Sufficient disk space** (videos are large files)
- **Processing power** (encoding is CPU-intensive)

#### Installation Steps

1. **Install FFmpeg:**

   ```bash
   # macOS
   brew install ffmpeg

   # Ubuntu/Debian
   sudo apt update && sudo apt install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/download.html
   ```

2. **Verify FFmpeg installation:**

   ```bash
   ffmpeg -version
   ffprobe -version
   ```

3. **No additional Python packages required** (uses only standard library)

### Performance & Optimization

#### Processing Time

- **Per minute of audio**: ~2-5 minutes processing time
- **Factors affecting speed**:
  - CPU performance (more cores = faster)
  - Image resolution (4K images process slower)
  - Audio length (longer = more processing)
  - Storage speed (SSD recommended)

#### Resource Usage

- **CPU**: High usage during encoding (uses multiple cores)
- **Memory**: ~1-2GB per video being processed
- **Disk**: Temporary space needed (2-3x final video size)
- **Storage**: Final videos are large (see file sizes above)

#### Optimization Tips

- **Use SSD storage** for faster I/O operations
- **Process during off-peak hours** for system performance
- **Batch process** multiple videos overnight
- **Monitor disk space** - videos consume significant storage
- **Use appropriate image sizes** - don't use 8K images for 4K output

### Quality Settings Explained

#### Video Quality (CRF Values)

- **CRF 18** (default): High quality, larger files
- **CRF 23**: Good quality, balanced file size
- **CRF 28**: Lower quality, smaller files

To change quality, modify the script:

```python
'-crf', '23',  # Change from '18' to '23' for smaller files
```

#### Encoding Presets

- **veryslow** (default): Best quality, slowest encoding
- **slow**: Good quality, faster encoding
- **medium**: Balanced quality/speed
- **fast**: Lower quality, much faster

### Workflow Integration

#### For Music Releases

1. **Prepare Assets:**

   - Export high-quality audio files
   - Create or obtain high-resolution artwork
   - Organize files in folders

2. **Create Videos:**

   ```bash
   python create_youtube_video.py \
       --audio-folder "release/audio" \
       --image "release/cover.jpg" \
       --output-folder "release/videos"
   ```

3. **Upload to Platforms:**
   - YouTube: Direct upload of MP4 files
   - Instagram: May need format conversion
   - TikTok: Consider creating shorter versions

#### Integration with Auspex Records Website

The generated videos can be used for:

- **YouTube embedding** on release pages
- **Social media promotion** across platforms
- **Streaming platform submissions** (some accept video content)
- **Press kit materials** for media outlets

### Troubleshooting

#### Common Issues

**"ffmpeg not found" error:**

- Install FFmpeg: `brew install ffmpeg` (macOS) or equivalent
- Verify installation: `ffmpeg -version`
- Check PATH environment variable

**"No supported audio files found":**

- Verify audio files have supported extensions
- Check file permissions and accessibility
- Ensure audio folder path is correct

**Video creation fails:**

- Check available disk space (need 2-3x final video size)
- Verify image file is not corrupted
- Test with shorter audio file first

**Poor video quality:**

- Use higher resolution source images (4K recommended)
- Verify image aspect ratio (16:9 ideal)
- Check audio quality of source files

#### Debug Mode

Add verbose output by modifying FFmpeg command:

```python
# Add these flags to the cmd list in create_video()
'-v', 'verbose',  # Add after 'ffmpeg'
```

### Advanced Usage

#### Custom Video Settings

Modify the script for different requirements:

```python
# For 1080p videos instead of 4K
'-vf', 'scale=1920:1080',

# For different frame rates
'-framerate', '30',  # 30fps instead of 24fps

# For different audio quality
'-b:a', '192k',  # Lower bitrate for smaller files
```

#### Batch Processing Script

Create a wrapper script for multiple albums:

```bash
#!/bin/bash
for album in */; do
    python create_youtube_video.py \
        --audio-folder "$album/audio" \
        --image "$album/cover.jpg" \
        --output-folder "$album/videos"
done
```

### Best Practices

#### File Organization

- **Consistent naming**: Use clear, consistent file naming conventions
- **Folder structure**: Organize by album/release for easy management
- **Backup strategy**: Videos are time-consuming to recreate

#### Quality Control

- **Test first**: Process one track before batch processing
- **Verify output**: Check video quality and audio sync
- **Monitor resources**: Ensure sufficient disk space and processing power

#### YouTube Optimization

- **Thumbnails**: Create custom thumbnails (videos use static images)
- **Metadata**: Add proper titles, descriptions, and tags
- **Playlists**: Organize videos into album/release playlists
- **End screens**: Consider adding end screens for engagement

```

```
