# Auspex Records Website

A modern React website for Auspex Records, showcasing psychedelic trance music releases and live performances. Built with TypeScript and deployed on AWS with separate staging and production environments.

## ✨ Key Features

- **Unified Background System**: Optimized animation components for better performance
- **Code Quality**: ESLint, Prettier, and pre-commit hooks for consistent code
- **Testing Framework**: Vitest with React Testing Library
- **Multi-Environment**: Separate staging and production deployments

## Project Structure

```
auspex-website/
├── src/                    # Frontend React application
│   ├── components/         # Reusable React components
│   │   ├── ui/            # ShadCN UI components
│   │   └── __tests__/     # Component tests
│   ├── pages/             # Page components
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility functions
│   ├── test/              # Test setup and utilities
│   ├── types/             # TypeScript type definitions
│   └── utils/             # API utilities and helpers
├── terraform/             # Infrastructure as Code
│   ├── environments/      # Environment-specific configurations
│   │   ├── staging/       # Staging environment (stage.auspexrecords.com)
│   │   └── prod/          # Production environment (auspexrecords.com)
│   ├── modules/           # Reusable Terraform modules
│   │   ├── api/           # API Gateway and Lambda resources
│   │   ├── database/      # DynamoDB resources
│   │   ├── domains/       # Route53 and ACM resources
│   │   └── website/       # S3 and CloudFront resources
│   ├── bootstrap/         # Bootstrap resources for Terraform state
│   └── seed-data.sh       # Script to seed data into DynamoDB
├── lambda/                # Lambda function code
│   ├── index.ts           # API handler
│   ├── package.json       # Lambda dependencies
│   └── tsconfig.json      # TypeScript configuration
└── dist/                  # Built frontend assets (generated)
```

## Technology Stack

- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **ShadCN UI** components
- **Vite** for build tooling
- **AWS** (S3, CloudFront, API Gateway, Lambda, DynamoDB)
- **Terraform** for infrastructure

## Development Setup

1. Install dependencies:

   ```bash
   # Install frontend dependencies
   npm install

   # Install Lambda dependencies
   cd lambda
   npm install
   ```

2. Set up environment variables:

   ```bash
   # Create .env file in the project root for development:
   VITE_API_URL=https://ma1l0omu6k.execute-api.us-west-1.amazonaws.com  # Staging API
   ```

3. Start the development server:

   ```bash
   npm run dev
   ```

## 🛠️ Development Scripts

### Core Development

```bash
npm run dev              # Start development server
npm run build            # Build for production
npm run preview          # Preview built application
```

### Code Quality

```bash
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run format           # Format code with Prettier
npm run format:check     # Check code formatting
npm run type-check       # Run TypeScript type checking
```

### Testing

```bash
npm run test             # Run tests in watch mode
npm run test:run         # Run tests once
npm run test:coverage    # Run tests with coverage report
```

### Environment-Specific Builds

```bash
npm run build:staging    # Build with staging API
npm run build:prod       # Build with production API
```

## Deployment

The project uses automated deployment scripts for both environments:

### Deploy to Staging

```bash
npm run deploy:staging
```

### Deploy to Production

```bash
npm run deploy:prod
```

### Manual Build (if needed)

```bash
# Build for staging
npm run build:staging

# Build for production
npm run build:prod

# Generic build (uses .env file)
npm run build
```

## Infrastructure Deployment

### Prerequisites

1. Install Terraform:

   ```bash
   brew install terraform    # macOS
   # or use your system's package manager
   ```

2. Configure AWS credentials:
   ```bash
   aws configure
   # Enter your AWS access key, secret key, and default region
   ```

### Bootstrap (One-time setup)

Create the S3 bucket and DynamoDB table for Terraform state:

```bash
cd terraform/bootstrap
terraform init
terraform apply
```

### Deploy Staging Environment

```bash
cd terraform/environments/staging
terraform init
terraform plan    # Review the changes
terraform apply   # Deploy the infrastructure
```

### Deploy Production Environment

```bash
cd terraform/environments/prod
terraform init
terraform plan    # Review the changes
terraform apply   # Deploy the infrastructure
```

### Seed Database

```bash
cd terraform
./seed-data.sh staging  # Seed staging environment
./seed-data.sh prod     # Seed production environment
```

### Deploy Frontend

Use the automated deployment scripts (recommended):

```bash
npm run deploy:staging  # Deploy to staging
npm run deploy:prod     # Deploy to production
```

## Environments

| Environment    | URL                             | Purpose                 | Deploy Command           |
| -------------- | ------------------------------- | ----------------------- | ------------------------ |
| **Staging**    | https://stage.auspexrecords.com | Testing and development | `npm run deploy:staging` |
| **Production** | https://auspexrecords.com       | Live website            | `npm run deploy:prod`    |

## 🎯 Code Quality & Development Standards

### Automated Code Quality

- **ESLint** with TypeScript rules for code consistency
- **Prettier** for automatic code formatting
- **Pre-commit hooks** with Husky and lint-staged
- **TypeScript strict mode** for type safety
- **Vitest** with React Testing Library for testing

### Development Workflow

1. **Code** → ESLint checks syntax and style
2. **Format** → Prettier ensures consistent formatting
3. **Test** → Vitest runs unit tests
4. **Commit** → Pre-commit hooks run linting and formatting
5. **Deploy** → Automated build and deployment

### Architecture Improvements

- **Unified Background System**: Consolidated 3 animation components into 1
- **Optimized Dependencies**: Removed unused packages, moved build tools to devDependencies
- **Modular Components**: Clean separation of concerns with reusable components
- **Performance Optimized**: Reduced bundle size and improved loading times

## ✨ Features

- **Interactive Animations**: Dynamic background with floating album covers and particles
- **Music Discovery**: Browse releases with embedded YouTube videos
- **Live Performances**: Watch recorded live performance videos
- **Multi-Platform Access**: Direct links to Bandcamp, SoundCloud, Spotify, etc.
- **Download Options**: Multiple audio formats available
- **Responsive Design**: Optimized for all devices and screen sizes
- **Dark Theme**: Music-focused interface with orange accent colors
- **Glass Morphism UI**: Modern design with glass-effect elements

## Current Releases

1. Reflections EP by Oak Project
   - 5 tracks including Nature, For the wall climbers, Cleaning Energy, etc.

2. The Pots Of My Heart, Are Full Of Your Seeds by Paranoiac
   - 6 tracks including Its all how you look at it, Lentamente, Creo en el amor, etc.

3. Time Crystal by Maru Secrets
   - 2 tracks: The Missing Crystal and The Land Before Time

4. Ion Tentacles by Aeromancer
   - 9 tracks including What happened to you, Ion Tentacles, Palm Groove, etc.

5. Midnight Sanctuary by Caixedia Camista
   - 3 tracks: Qbit, End of Time, and See The Light

## Contributing

1. Create a feature branch
2. Make your changes
3. Submit a pull request

## License

All rights reserved © Auspex Records
