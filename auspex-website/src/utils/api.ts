import type { LivePerformance, Release } from '../types';

// TODO: Replace with actual API URL from environment variables
const API_URL = import.meta.env.VITE_API_URL;

if (!API_URL) {
  console.warn('API_URL environment variable is not set');
}

export const api = {
  async fetchReleases(): Promise<Release[]> {
    try {
      const response = await fetch(`${API_URL}/releases`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching releases:', error);
      throw error;
    }
  },

  async fetchPerformances(): Promise<LivePerformance[]> {
    try {
      const response = await fetch(`${API_URL}/performances`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching performances:', error);
      throw error;
    }
  },
};
