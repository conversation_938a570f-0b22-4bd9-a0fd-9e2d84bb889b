import { useEffect, useState } from 'react';
import { LivePerformanceCard } from '../components/LivePerformanceCard';
import type { LivePerformance } from '../types';
import { api } from '../utils/api';

export const LivePerformancesPage = () => {
  const [performances, setPerformances] = useState<LivePerformance[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPerformances = async () => {
      try {
        const data = await api.fetchPerformances();
        setPerformances(data);
        setLoading(false);
      } catch {
        setError('Failed to load live performances');
        setLoading(false);
      }
    };

    fetchPerformances();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-white">Loading live performances...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-4xl font-bold text-white mb-8">Live Performances</h1>
      <div className="space-y-8">
        {performances.map(performance => (
          <LivePerformanceCard key={performance.id} performance={performance} />
        ))}
      </div>
    </div>
  );
};
