import { useEffect, useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { ReleaseCard } from '../components/ReleaseCard';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { UnifiedBackground } from '@/components/UnifiedBackground';
import { useScrollReveal, scrollRevealVariants } from '@/hooks/useScrollReveal';
import type { Release } from '../types';
import { api } from '../utils/api';

export const ReleasesPage = () => {
  const [releases, setReleases] = useState<Release[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<string | null | undefined>(undefined);
  const { ref: titleRef, isVisible: titleVisible } = useScrollReveal({ threshold: 0.2 });
  const { ref: sidebarRef, isVisible: sidebarVisible } = useScrollReveal({ threshold: 0.3 });

  useEffect(() => {
    const fetchReleases = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await api.fetchReleases();
        setReleases(data);
      } catch (err) {
        console.error('Error fetching releases:', err);
        setError('Failed to load releases. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchReleases();
  }, []);

  const availableYears = useMemo(() => {
    const years = releases.map(release => new Date(release.releaseDate).getFullYear());
    return [...new Set(years)].sort((a, b) => b - a);
  }, [releases]);

  const filteredReleases = useMemo(() => {
    if (selectedYear === null) return releases;
    if (selectedYear === undefined) {
      const currentYear = new Date().getFullYear();
      return releases.filter(
        release => new Date(release.releaseDate).getFullYear() === currentYear
      );
    }
    return releases.filter(
      release => new Date(release.releaseDate).getFullYear() === parseInt(selectedYear)
    );
  }, [releases, selectedYear]);

  const groupedReleases = useMemo(() => {
    const groups: { [year: number]: Release[] } = {};
    filteredReleases.forEach(release => {
      const year = new Date(release.releaseDate).getFullYear();
      if (!groups[year]) groups[year] = [];
      groups[year].push(release);
    });

    // Sort releases within each year by date (newest first)
    Object.keys(groups).forEach(year => {
      groups[parseInt(year)].sort(
        (a, b) => new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime()
      );
    });

    return groups;
  }, [filteredReleases]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6, ease: [0.6, -0.05, 0.01, 0.99] },
    },
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-auspex-navy-950 via-auspex-navy-900 to-auspex-navy-950 flex items-center justify-center">
        <motion.div
          className="text-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-auspex-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-300 text-lg font-body">Loading releases...</p>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-auspex-navy-950 via-auspex-navy-900 to-auspex-navy-950 flex items-center justify-center">
        <motion.div
          className="text-center max-w-md mx-auto px-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="glass-effect p-8 rounded-2xl border border-red-500/30">
            <h2 className="text-2xl font-heading font-bold text-red-400 mb-4">Error</h2>
            <p className="text-gray-300 font-body mb-6">{error}</p>
            <motion.button
              onClick={() => window.location.reload()}
              className="bg-gradient-primary px-6 py-3 rounded-full text-white font-semibold hover-lift"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Try Again
            </motion.button>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="relative min-h-screen">
      <UnifiedBackground variant="particles" enableInteractiveParticles={true} className="z-0" />
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <motion.h1
          ref={titleRef}
          className="text-5xl md:text-6xl font-heading font-bold text-gradient mb-12 text-center lg:text-left"
          variants={scrollRevealVariants.fadeInUp}
          initial="hidden"
          animate={titleVisible ? 'visible' : 'hidden'}
        >
          Releases
        </motion.h1>

        <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
          {/* Sidebar */}
          <motion.div
            ref={sidebarRef}
            className="lg:col-span-3 order-first"
            variants={scrollRevealVariants.fadeInLeft}
            initial="hidden"
            animate={sidebarVisible ? 'visible' : 'hidden'}
          >
            <Card className="ultra-glass p-6 border border-auspex-navy-400/30 sticky top-24">
              <CardContent className="p-0">
                <h3 className="text-2xl font-heading font-bold text-white mb-6">Browse by Year</h3>
                <div className="space-y-3">
                  {availableYears.map(year => (
                    <Button
                      key={year}
                      onClick={() => setSelectedYear(year.toString())}
                      variant={
                        selectedYear === year.toString() ||
                        (selectedYear === undefined && year === new Date().getFullYear())
                          ? 'default'
                          : 'ghost'
                      }
                      className={`w-full justify-start px-4 py-3 font-body font-medium transition-all duration-300 ${
                        selectedYear === year.toString() ||
                        (selectedYear === undefined && year === new Date().getFullYear())
                          ? 'bg-gradient-primary text-white shadow-glow hover:bg-gradient-secondary'
                          : 'text-gray-300 hover:text-white hover:bg-auspex-navy-800/50 liquid-glass'
                      }`}
                    >
                      {year}
                    </Button>
                  ))}
                  <Button
                    onClick={() => setSelectedYear(null)}
                    variant={selectedYear === null ? 'default' : 'ghost'}
                    className={`w-full justify-start px-4 py-3 font-body font-medium transition-all duration-300 ${
                      selectedYear === null
                        ? 'bg-gradient-primary text-white shadow-glow hover:bg-gradient-secondary'
                        : 'text-gray-300 hover:text-white hover:bg-auspex-navy-800/50 liquid-glass'
                    }`}
                  >
                    All Years
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Main Content */}
          <motion.div
            className="lg:col-span-9 order-last"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {Object.entries(groupedReleases)
              .sort(([a], [b]) => parseInt(b) - parseInt(a))
              .map(([year, yearReleases]) => (
                <motion.div key={year} className="mb-16" variants={itemVariants}>
                  <h2 className="text-4xl font-heading font-bold text-gradient-secondary mb-8">
                    {year}
                  </h2>
                  <div className="space-y-12">
                    {yearReleases.map((release, index) => (
                      <motion.div
                        key={release.id}
                        variants={itemVariants}
                        transition={{ delay: index * 0.1 }}
                      >
                        <ReleaseCard release={release} />
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              ))}

            {filteredReleases.length === 0 && (
              <motion.div className="text-center py-20" variants={itemVariants}>
                <div className="glass-effect p-12 rounded-2xl border border-auspex-navy-400/30">
                  <h3 className="text-2xl font-heading font-bold text-gray-300 mb-4">
                    No releases found
                  </h3>
                  <p className="text-gray-400 font-body">
                    {selectedYear === null
                      ? 'No releases available at the moment.'
                      : `No releases found for ${selectedYear || new Date().getFullYear()}.`}
                  </p>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
    </div>
  );
};
