import { render } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { UnifiedBackground } from '../UnifiedBackground';

describe('UnifiedBackground', () => {
  it('renders without crashing', () => {
    render(<UnifiedBackground />);
    // The component should render without throwing an error
    expect(document.body).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<UnifiedBackground className="test-class" />);
    const backgroundElement = container.firstChild as HTMLElement;
    expect(backgroundElement).toHaveClass('test-class');
  });

  it('renders collage variant by default', () => {
    const { container } = render(<UnifiedBackground />);
    const backgroundElement = container.firstChild as HTMLElement;
    expect(backgroundElement).toBeInTheDocument();
  });

  it('renders particles variant', () => {
    const { container } = render(<UnifiedBackground variant="particles" />);
    const backgroundElement = container.firstChild as HTMLElement;
    expect(backgroundElement).toBeInTheDocument();
  });

  it('renders minimal variant', () => {
    const { container } = render(<UnifiedBackground variant="minimal" />);
    const backgroundElement = container.firstChild as HTMLElement;
    expect(backgroundElement).toBeInTheDocument();
  });
});
