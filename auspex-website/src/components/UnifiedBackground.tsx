import { useEffect, useState, useCallback, useRef, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

// Release covers data
const releaseCovers = [
  '/assets/release-covers/Aeromancer - Ion Tentacles.jpg',
  '/assets/release-covers/Caixedia Camista - Midnight Sanctuary.jpg',
  '/assets/release-covers/Haavi - Wisdom of the World Vol. 1.jpg',
  '/assets/release-covers/Hunting Hush - Moksha Island.jpg',
  '/assets/release-covers/Maru Secrets - Time Crystal.jpg',
  '/assets/release-covers/Oak Project - Reflections.jpeg',
  '/assets/release-covers/Paranoiac - Pots.jpg',
  '/assets/release-covers/Samyaza - II.jpg',
  '/assets/release-covers/Zaar - Psykopomps.jpg',
];

// Interfaces
interface UnifiedBackgroundProps {
  variant?: 'collage' | 'particles' | 'minimal';
  enableInteractiveParticles?: boolean;
  className?: string;
}

interface FloatingCover {
  id: string;
  src: string;
  x: number;
  y: number;
  rotation: number;
  scale: number;
  duration: number;
  appearTime: number;
  lifespan: number;
}

interface Particle {
  id: number;
  x: number;
  y: number;
  vx: number;
  vy: number;
  size: number;
  color: string;
  opacity: number;
}

export const UnifiedBackground = ({
  variant = 'collage',
  enableInteractiveParticles = false,
  className = '',
}: UnifiedBackgroundProps) => {
  // Collage animation state
  const [activeCovers, setActiveCovers] = useState<FloatingCover[]>([]);
  const [isStarted, setIsStarted] = useState(false);
  const intervalRef = useRef<number | null>(null);
  const coverIdCounter = useRef(0);
  const usedCovers = useRef<Set<string>>(new Set());
  const availableCovers = useRef<string[]>([...releaseCovers]);

  // Interactive particles state
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();

  const colors = useMemo(
    () => [
      'rgba(255, 127, 0, 0.6)', // Orange
      'rgba(79, 99, 255, 0.6)', // Blue
      'rgba(159, 122, 255, 0.6)', // Purple
      'rgba(255, 255, 255, 0.4)', // White
    ],
    []
  );

  // Collage animation functions
  const generatePosition = useCallback(() => {
    return {
      x: Math.random() * 70 + 15, // 15% to 85% to avoid edges
      y: Math.random() * 70 + 15, // 15% to 85% to avoid edges
    };
  }, []);

  const getNextCoverSrc = useCallback(() => {
    if (availableCovers.current.length === 0) {
      availableCovers.current = [...releaseCovers];
      usedCovers.current.clear();
    }

    const randomIndex = Math.floor(Math.random() * availableCovers.current.length);
    const selectedCover = availableCovers.current[randomIndex];

    availableCovers.current.splice(randomIndex, 1);
    usedCovers.current.add(selectedCover);

    return selectedCover;
  }, []);

  const createNewCover = useCallback(() => {
    const coverSrc = getNextCoverSrc();
    const position = generatePosition();

    return {
      id: `cover-${coverIdCounter.current++}`,
      src: coverSrc,
      x: position.x,
      y: position.y,
      rotation: Math.random() * 360,
      scale: 0.6 + Math.random() * 0.3, // 0.6 to 0.9 scale
      duration: 12 + Math.random() * 8, // 12-20 seconds for floating animation
      appearTime: Date.now(),
      lifespan: 6000 + Math.random() * 8000, // 6-14 seconds before disappearing
    };
  }, [generatePosition, getNextCoverSrc]);

  const isCoverActive = useCallback((src: string, activeCovers: FloatingCover[]) => {
    return activeCovers.some(cover => cover.src === src);
  }, []);

  // Start collage animation
  useEffect(() => {
    if (variant === 'collage') {
      const timer = setTimeout(() => setIsStarted(true), 1000);
      return () => clearTimeout(timer);
    }
  }, [variant]);

  // Collage animation loop
  useEffect(() => {
    if (variant === 'collage' && isStarted) {
      intervalRef.current = setInterval(() => {
        setActiveCovers(prev => {
          const now = Date.now();
          // Remove expired covers
          const activeCover = prev.filter(cover => now - cover.appearTime < cover.lifespan);

          // Try to maintain 4-5 covers
          const newCovers = [...activeCover];

          // Add new covers if we have less than 4, ensuring no duplicates
          while (newCovers.length < 4) {
            let attempts = 0;
            let newCover;

            do {
              newCover = createNewCover();
              attempts++;
            } while (isCoverActive(newCover.src, newCovers) && attempts < 10);

            if (!isCoverActive(newCover.src, newCovers)) {
              newCovers.push(newCover);
            } else {
              break; // Can't find unique cover, stop trying
            }
          }

          // Randomly add one more cover up to 5 total if possible
          if (newCovers.length < 5 && Math.random() > 0.4) {
            let attempts = 0;
            let newCover;

            do {
              newCover = createNewCover();
              attempts++;
            } while (isCoverActive(newCover.src, newCovers) && attempts < 10);

            if (!isCoverActive(newCover.src, newCovers)) {
              newCovers.push(newCover);
            }
          }

          return newCovers;
        });
      }, 1500); // Check every 1.5 seconds for faster cycling
    }
  }, [isStarted, createNewCover, isCoverActive, variant]);

  // Interactive particles setup
  useEffect(() => {
    if (!enableInteractiveParticles || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let localParticles: Particle[] = [];
    let localMousePosition = { x: 0, y: 0 };

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    const createParticles = () => {
      const particleCount = Math.min(30, Math.floor(window.innerWidth / 40));
      localParticles = [];

      for (let i = 0; i < particleCount; i++) {
        localParticles.push({
          id: i,
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.3,
          vy: (Math.random() - 0.5) * 0.3,
          size: Math.random() * 2 + 1,
          color: colors[Math.floor(Math.random() * colors.length)],
          opacity: Math.random() * 0.4 + 0.1,
        });
      }
    };

    const updateParticles = () => {
      localParticles.forEach(particle => {
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Mouse interaction
        const dx = localMousePosition.x - particle.x;
        const dy = localMousePosition.y - particle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance < 80) {
          const force = (80 - distance) / 80;
          particle.x -= dx * force * 0.005;
          particle.y -= dy * force * 0.005;
        }

        // Boundary collision
        if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
        if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

        particle.x = Math.max(0, Math.min(canvas.width, particle.x));
        particle.y = Math.max(0, Math.min(canvas.height, particle.y));
      });
    };

    const drawParticles = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      localParticles.forEach(particle => {
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.globalAlpha = particle.opacity;
        ctx.fill();

        // Draw connections (limited for performance)
        localParticles.forEach(otherParticle => {
          if (particle.id !== otherParticle.id && particle.id < otherParticle.id) {
            const dx = particle.x - otherParticle.x;
            const dy = particle.y - otherParticle.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance < 100) {
              ctx.beginPath();
              ctx.moveTo(particle.x, particle.y);
              ctx.lineTo(otherParticle.x, otherParticle.y);
              ctx.strokeStyle = `rgba(255, 127, 0, ${0.05 * (1 - distance / 100)})`;
              ctx.lineWidth = 0.5;
              ctx.stroke();
            }
          }
        });
      });

      ctx.globalAlpha = 1;
    };

    const animate = () => {
      updateParticles();
      drawParticles();
      animationRef.current = requestAnimationFrame(animate);
    };

    const handleResize = () => {
      resizeCanvas();
      createParticles();
    };

    const handleMouseMove = (e: MouseEvent) => {
      localMousePosition = { x: e.clientX, y: e.clientY };
    };

    resizeCanvas();
    createParticles();
    animate();

    window.addEventListener('resize', handleResize);
    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('mousemove', handleMouseMove);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [colors, enableInteractiveParticles]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const renderCollage = () => (
    <AnimatePresence mode="sync">
      {activeCovers.map(cover => (
        <motion.div
          key={cover.id}
          className="absolute pointer-events-none"
          style={{
            left: `${cover.x}%`,
            top: `${cover.y}%`,
          }}
          initial={{
            opacity: 0,
            scale: 0,
            rotate: cover.rotation - 90,
          }}
          animate={{
            opacity: [0, 0.8, 0.6, 0.8],
            scale: [0, cover.scale * 1.2, cover.scale, cover.scale * 1.1, cover.scale],
            rotate: [
              cover.rotation - 90,
              cover.rotation,
              cover.rotation + 180,
              cover.rotation + 270,
            ],
            x: [0, 20, -15, 10, 0],
            y: [0, -15, 25, -10, 0],
          }}
          exit={{
            opacity: 0,
            scale: 0,
            rotate: cover.rotation + 180,
            transition: {
              duration: 1.0,
              ease: 'easeInOut',
            },
          }}
          transition={{
            duration: cover.duration,
            repeat: Infinity,
            ease: 'easeInOut',
            times: [0, 0.25, 0.5, 0.75, 1],
          }}
        >
          <div className="relative">
            <img
              src={cover.src}
              alt="Release cover"
              className="w-28 h-28 md:w-40 md:h-40 lg:w-48 lg:h-48 rounded-xl shadow-2xl border border-auspex-orange-500/30 transition-all duration-500"
              loading="lazy"
              draggable={false}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-auspex-navy-900/50 via-transparent to-auspex-orange-500/20 rounded-xl opacity-70" />
          </div>
        </motion.div>
      ))}
    </AnimatePresence>
  );

  const renderParticles = () => (
    <div className="absolute inset-0">
      {Array.from({ length: 25 }, (_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full"
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
            width: `${2 + Math.random() * 4}px`,
            height: `${2 + Math.random() * 4}px`,
          }}
          initial={{ opacity: 0 }}
          animate={{
            opacity: [0, 0.6, 0],
            y: [0, -200, -400],
            x: [0, Math.sin(i) * 50, Math.cos(i) * 30],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: 10 + Math.random() * 20,
            repeat: Infinity,
            delay: Math.random() * 5,
            ease: 'easeOut',
          }}
        >
          <div
            className="w-full h-full rounded-full"
            style={{
              background: `radial-gradient(circle, ${
                i % 3 === 0 ? '#FF4F00' : i % 3 === 1 ? '#9333EA' : '#22D3EE'
              }40, transparent)`,
            }}
          />
        </motion.div>
      ))}
    </div>
  );

  const renderMinimal = () => (
    <div className="absolute inset-0">
      <motion.div
        className="absolute inset-0 opacity-30"
        animate={{
          background: [
            'radial-gradient(circle at 20% 80%, #FF4F0010 0%, transparent 50%)',
            'radial-gradient(circle at 80% 20%, #9333EA10 0%, transparent 50%)',
            'radial-gradient(circle at 40% 40%, #22D3EE10 0%, transparent 50%)',
            'radial-gradient(circle at 20% 80%, #FF4F0010 0%, transparent 50%)',
          ],
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          ease: 'linear',
        }}
      />
    </div>
  );

  const renderContent = () => {
    switch (variant) {
      case 'collage':
        return renderCollage();
      case 'particles':
        return renderParticles();
      case 'minimal':
        return renderMinimal();
      default:
        return renderCollage();
    }
  };

  return (
    <div className={`absolute inset-0 overflow-hidden ${className}`}>
      {/* Base gradient background */}
      <div className="absolute inset-0 bg-gradient-to-br from-auspex-navy-950 via-auspex-navy-900 to-auspex-navy-950" />

      {/* Interactive particles canvas */}
      {enableInteractiveParticles && (
        <motion.canvas
          ref={canvasRef}
          className="absolute inset-0 pointer-events-none z-0"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 2 }}
          style={{ background: 'transparent' }}
        />
      )}

      {/* Main content */}
      {renderContent()}

      {/* Overlay gradients for better content readability */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/40" />
      <div className="absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20" />
    </div>
  );
};
