import type { LivePerformance } from '../types';
import { YouTubeEmbed } from './YouTubeEmbed';

interface LivePerformanceCardProps {
  performance: LivePerformance;
}

export const LivePerformanceCard = ({ performance }: LivePerformanceCardProps) => {
  return (
    <div className="ultra-glass rounded-xl overflow-hidden shadow-2xl border border-auspex-navy-400/30 hover:border-auspex-orange-400/50 transition-all duration-300">
      <div className="p-6">
        <div className="space-y-4">
          {/* Video Embed */}
          <YouTubeEmbed videoId={performance.youtubeVideoId} title={performance.title} />

          {/* Performance Details */}
          <div className="mt-4">
            <h2 className="text-2xl font-bold text-white">{performance.title}</h2>
            <h3 className="text-xl text-auspex-orange-400 mb-2">{performance.artist}</h3>
            <p className="text-gray-300 mb-4">
              {new Date(performance.date + 'T00:00:00').toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
              })}
            </p>
            <p className="text-gray-200 leading-relaxed">{performance.description}</p>
          </div>

          {/* YouTube Link */}
          <div className="mt-4">
            <a
              href={`https://www.youtube.com/watch?v=${performance.youtubeVideoId}`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center liquid-glass bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-6 py-3 rounded-full text-sm font-semibold shadow-glow hover:shadow-glow-strong transition-all duration-300"
            >
              Watch on YouTube
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};
