#!/bin/bash

# Check if environment is provided
if [ -z "$1" ]; then
  echo "Usage: ./seed-data.sh <environment>"
  echo "Example: ./seed-data.sh staging"
  exit 1
fi

ENVIRONMENT=$1
ENV_DIR="environments/$ENVIRONMENT"

# Change to the environment directory
cd $ENV_DIR || { echo "Environment directory not found"; exit 1; }

# Get table names from Terraform outputs
RELEASES_TABLE=$(terraform output -raw dynamodb_releases_table)
PERFORMANCES_TABLE=$(terraform output -raw dynamodb_performances_table)

# Seed releases
echo "Seeding releases..."

# Reflections EP
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "1"},
    "title": {"S": "Reflections"},
    "artist": {"S": "Oak Project"},
    "coverUrl": {"S": "/assets/release-covers/Oak Project - Reflections.jpg"},
    "releaseDate": {"S": "2024-04-20"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Nature"}, "youtubeVideoId": {"S": "7fzP2whPMEc"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "For the wall climbers"}, "youtubeVideoId": {"S": "jR4xpX5ZVas"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Cleaning Energy"}, "youtubeVideoId": {"S": "2KTAHIrcML8"}}},
      {"M": {"id": {"S": "4"}, "title": {"S": "Olar a pasto"}, "youtubeVideoId": {"S": "QuEp1rvc4iU"}}},
      {"M": {"id": {"S": "5"}, "title": {"S": "63.30"}, "youtubeVideoId": {"S": "dwO8VetxvoA"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/reflections-ep"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/oak-project-reflections"},
      "spotify": {"S": "https://open.spotify.com/album/1FgTZMkNYFmn94qqQ9RiqY"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/reflections-ep/1762030830"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_lGb6GABLe8BAcBoiypSW-32go09xslLCs"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0DCP8LLRV"}
    }}
  }'

# The Pots Of My Heart
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "2"},
    "title": {"S": "The Pots Of My Heart Are Full Of Your Seeds"},
    "artist": {"S": "Paranoiac"},
    "coverUrl": {"S": "/assets/release-covers/Paranoiac - Pots.jpg"},
    "releaseDate": {"S": "2024-06-04"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Its all how you look at it."}, "youtubeVideoId": {"S": "oYGEWxl20bg"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "Lentamente"}, "youtubeVideoId": {"S": "IaIeiZmI5rI"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Creo en el amor"}, "youtubeVideoId": {"S": "kilkBw6yf5w"}}},
      {"M": {"id": {"S": "4"}, "title": {"S": "Aquel dia.."}, "youtubeVideoId": {"S": "z9zJbxv4dgs"}}},
      {"M": {"id": {"S": "5"}, "title": {"S": "VOFI"}, "youtubeVideoId": {"S": "_MXCAJ0tGP8"}}},
      {"M": {"id": {"S": "6"}, "title": {"S": "A Babaji Hitech tribute is like a party with police..."}, "youtubeVideoId": {"S": "4XPynWVpryo"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/the-pots-of-my-heart-are-full-of-your-seeds"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/the-pots-of-my-heart-are-full-of-your-seeds"},
      "spotify": {"S": "https://open.spotify.com/album/08PmrZri0OV1s52IPSpAxd"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/the-pots-of-my-heart-are-full-of-your-seeds/1754683294"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_mjnUSY8FFsGtGz_4jA-1fj4yemATN0Nr4"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0D884YD46"}
    }}
  }'

# Time Crystal
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "3"},
    "title": {"S": "Time Crystal"},
    "artist": {"S": "Maru Secrets"},
    "coverUrl": {"S": "/assets/release-covers/Maru Secrets - Time Crystal.jpg"},
    "releaseDate": {"S": "2024-08-08"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "The Missing Crystal"}, "youtubeVideoId": {"S": "ZokDafdfAng"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "The Land Before Time"}, "youtubeVideoId": {"S": "Kf9CHYRrQ1M"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/time-crystal"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/maru-secrets-time-crystal"}
    }}
  }'

# Ion Tentacles
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "4"},
    "title": {"S": "Ion Tentacles"},
    "artist": {"S": "Aeromancer"},
    "coverUrl": {"S": "/assets/release-covers/Aeromancer - Ion Tentacles.jpg"},
    "releaseDate": {"S": "2024-08-20"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "What happened to you"}, "youtubeVideoId": {"S": "4Rr-OBFWAvo"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "Ion Tentacles"}, "youtubeVideoId": {"S": "Okk3hL-I65k"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Palm Groove"}, "youtubeVideoId": {"S": "QlnTy6-i2BU"}}},
      {"M": {"id": {"S": "4"}, "title": {"S": "Aero2000"}, "youtubeVideoId": {"S": "xVOQBvKEBBg"}}},
      {"M": {"id": {"S": "5"}, "title": {"S": "Spaceborne Abomination"}, "youtubeVideoId": {"S": "GijVnLrmcgA"}}},
      {"M": {"id": {"S": "6"}, "title": {"S": "Corruption"}, "youtubeVideoId": {"S": "1mpHGOqZBtk"}}},
      {"M": {"id": {"S": "7"}, "title": {"S": "Alien Worlds"}, "youtubeVideoId": {"S": "aM32A9uYzbQ"}}},
      {"M": {"id": {"S": "8"}, "title": {"S": "Om Namah Shivay"}, "youtubeVideoId": {"S": "hcrv3Mv5_Kw"}}},
      {"M": {"id": {"S": "9"}, "title": {"S": "Har Har Mahadev"}, "youtubeVideoId": {"S": "uOh8oOD-vo0"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/ion-tentacles"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/aeromancer-ion-tentacles"},
      "spotify": {"S": "https://open.spotify.com/album/1B6d8Jh6BSChyeG32lvVjt"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/ion-tentacles/1764469007"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_nzszAl6JXxBDDlRnAdWgnaJGjeF_xK-BA"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0DF2K4MYK"}
    }}
  }'

# Midnight Sanctuary
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "5"},
    "title": {"S": "Midnight Sanctuary"},
    "artist": {"S": "Caixedia Camista"},
    "coverUrl": {"S": "/assets/release-covers/Caixedia Camista - Midnight Sanctuary.jpg"},
    "releaseDate": {"S": "2024-10-11"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Qbit"}, "youtubeVideoId": {"S": "t9nAwic2a-E"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "End of Time"}, "youtubeVideoId": {"S": "RHUCIWbavxM"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "See The Light"}, "youtubeVideoId": {"S": "hPKS1AIqk_g"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Where are you"}, "youtubeVideoId": {"S": "CiuruqDTSOo"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Midnight Sanctuary"}, "youtubeVideoId": {"S": "UjOzC85OprU"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Mea Culpa"}, "youtubeVideoId": {"S": "1xWN64Us2Ts"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/midnight-sanctuary"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/caixedia-camista-midnight-sanctuary-1"},
      "spotify": {"S": "https://open.spotify.com/album/7cgxmM9EmLtKDYa7LE63Pl"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/midnight-sanctuary/1774298366"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_lI6swDuE4siSTTp5a8VCNTQpP-M_CSpaE"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0DJY3VVD7"}
    }}
  }'

# Moksha Island
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "6"},
    "title": {"S": "Moksha Island"},
    "artist": {"S": "Hunting Hush"},
    "coverUrl": {"S": "/assets/release-covers/Hunting Hush - Moksha Island.jpg"},
    "releaseDate": {"S": "2025-01-12"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Hunting Hush & Multidimensional - Seashore Symphony"}, "youtubeVideoId": {"S": "w8C-HSHbC_g"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "Lucid Aspirations"}, "youtubeVideoId": {"S": "3424rf8W8FE"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Cognitive Landscapes"}, "youtubeVideoId": {"S": "K-eImqLSovY"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/moksha-island"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/moksha-island"},
      "spotify": {"S": "https://open.spotify.com/album/4MlLDnbAJRj1s4I9qok7id"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/moksha-island-single/1795659440"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_mK9NUxsduRhMcSpewt3RlQMsLPY2AnYBc"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0DWSWTGVW"}
    }}
  }'

# II
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "7"},
    "title": {"S": "II"},
    "artist": {"S": "Samyaza"},
    "coverUrl": {"S": "/assets/release-covers/Samyaza - II.jpg"},
    "releaseDate": {"S": "2025-01-13"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Shadow Puppets"}, "youtubeVideoId": {"S": "w7wp9Ma9H9w"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "Silver River"}, "youtubeVideoId": {"S": "PYC3V2viF5E"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Marios Glacier"}, "youtubeVideoId": {"S": "KRz_BIceRjM"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Schematics"}, "youtubeVideoId": {"S": "HeLu3nS-hTQ"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Mulberry"}, "youtubeVideoId": {"S": "FeBDNt0zQGI"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Marish"}, "youtubeVideoId": {"S": "zij5MoCwq4A"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/ii"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/samyaza-ii"},
      "spotify": {"S": "https://open.spotify.com/album/04bksZsofx7SK8yiUAHNnc"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/ii/1790422758"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_m8kKOFlMFtUrYvoBv0Qej8khsOrXQRk-0"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0DT1HX41M"}
    }}
  }'

# Wisdom of the World Vol. 1
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "8"},
    "title": {"S": "Wisdom of the World Vol. 1"},
    "artist": {"S": "Haavi"},
    "coverUrl": {"S": "/assets/release-covers/Haavi - Wisdom of the World Vol. 1.jpg"},
    "releaseDate": {"S": "2025-01-26"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Doryoko WA Uragiranai"}, "youtubeVideoId": {"S": "b-RGanFs610"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "Day Trip in Bali"}, "youtubeVideoId": {"S": "CwxB23L5KPI"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Haavi & Multidimensional Live - tHe hUNT"}, "youtubeVideoId": {"S": "4bNlLt4FBzo"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/wisdom-of-the-world-vol-1"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/wisdom-of-the-wold-vol-1"},
      "spotify": {"S": "https://open.spotify.com/album/3xrmy9CuUf2qvfbPEkToeu"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/wisdom-of-the-world-vol-1-ep/1796408495"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_l7h5LovHYX-LnNA5ac0A_w4gzn6NVwMbg"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0DVR3QKG2"}
    }}
  }'

# Psykopomps
aws dynamodb put-item \
  --table-name $RELEASES_TABLE \
  --item '{
    "id": {"S": "9"},
    "title": {"S": "Psykopomps"},
    "artist": {"S": "Zaar"},
    "coverUrl": {"S": "/assets/release-covers/Zaar - Psykopomps.jpg"},
    "releaseDate": {"S": "2025-06-01"},
    "tracks": {"L": [
      {"M": {"id": {"S": "1"}, "title": {"S": "Guided by voices"}, "youtubeVideoId": {"S": "TWFiNbs21qM"}}},
      {"M": {"id": {"S": "2"}, "title": {"S": "Gate Opener"}, "youtubeVideoId": {"S": "FjrJJarXD5I"}}},
      {"M": {"id": {"S": "3"}, "title": {"S": "Anno2000"}, "youtubeVideoId": {"S": "yJubXOkRJrE"}}},
      {"M": {"id": {"S": "4"}, "title": {"S": "X-ces Files"}, "youtubeVideoId": {"S": "DnzCmCZssm4"}}},
      {"M": {"id": {"S": "5"}, "title": {"S": "Black Dog"}, "youtubeVideoId": {"S": "3tMCraBQdE0"}}},
      {"M": {"id": {"S": "6"}, "title": {"S": "Carousell"}, "youtubeVideoId": {"S": "SSQHJV7kTUo"}}},
      {"M": {"id": {"S": "7"}, "title": {"S": "Miles Away"}, "youtubeVideoId": {"S": "Qp1LAHLDosY"}}},
      {"M": {"id": {"S": "8"}, "title": {"S": "Digital Math"}, "youtubeVideoId": {"S": "zLHX190wRkY"}}},
      {"M": {"id": {"S": "9"}, "title": {"S": "Journey to the light"}, "youtubeVideoId": {"S": "DXXJT9k0pmE"}}}
    ]},
    "platforms": {"M": {
      "bandcamp": {"S": "https://auspexrecords.bandcamp.com/album/psykopomps"},
      "soundcloud": {"S": "https://soundcloud.com/auspexrecords/sets/zaar-psykopomps"},
      "spotify": {"S": "https://open.spotify.com/album/4sp5h6k08aO4RiVBTLBq1d"},
      "appleMusicUrl": {"S": "https://music.apple.com/us/album/psykopomps/1830662931"},
      "youtubeMusicUrl": {"S": "https://music.youtube.com/playlist?list=OLAK5uy_lDOL564W7gBwANEQRbmXXXgZVrLxRQjTM"},
      "amazonMusicUrl": {"S": "https://music.amazon.com/albums/B0FKWG8M57"}
    }}
  }'

# Seed performances
echo "Seeding performances..."

aws dynamodb put-item \
  --table-name $PERFORMANCES_TABLE \
  --item '{
    "id": {"S": "1"},
    "title": {"S": "Aeromancer Live 2024"},
    "artist": {"S": "Aeromancer"},
    "youtubeVideoId": {"S": "FkcYXnxwqGc"},
    "date": {"S": "2024-08-01"},
    "description": {"S": "Aeromancer performing live at Santa Cruz, California, USA"}
  }'

echo "Data seeding completed successfully"
